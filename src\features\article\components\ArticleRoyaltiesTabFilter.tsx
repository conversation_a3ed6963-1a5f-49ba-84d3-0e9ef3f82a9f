import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Nav, NavItem, NavLink } from 'reactstrap';
import { ArticleTypeNames } from '../../../types/common/Item';
import { NewArticleType } from '../../../types/Article';

interface IProps {
    activeTab: string;
    onTabChange: (tabId: string) => void;
    setIsOpenArticleRoyaltyModal: Dispatch<SetStateAction<boolean>>;
}

export default function ArticleRoyaltiesTabFilter({ activeTab, onTabChange, setIsOpenArticleRoyaltyModal }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [tabs, setTabs] = useState<Array<{ id: string; name: string }>>([]);

    useEffect(() => {
        // Bỏ tab 'Chưa phân loại' (NewArticleType.UNCLASSIFIED)
        const allTabs = [
            { id: NewArticleType.ALL, name: '<PERSON><PERSON><PERSON> c<PERSON>' },
            ...ArticleTypeNames.map((type) => ({ id: type.id.toString(), name: t(`${type.name}.single`) })),
        ];
        setTabs(allTabs);
    }, [t]);

    return (
        <div className="card">
            <div className="card-body">
                <div className="row">
                    <div className="col-12 col-md-8">
                        <Nav tabs className="mb-2">
                            {tabs.map((tab) => (
                                <NavItem key={tab.id}>
                                    <NavLink
                                        className={activeTab === tab.id ? 'active' : ''}
                                        onClick={() => onTabChange(tab.id)}
                                    >
                                        {tab.name}
                                    </NavLink>
                                </NavItem>
                            ))}
                        </Nav>
                    </div>
                    <div className="col-12 col-md-4">
                        <div className="flex justify-end">
                            <button
                                type="button"
                                className="btn btn-primary"
                                onClick={() => setIsOpenArticleRoyaltyModal(true)}
                            >
                                Chấm nhuận bút
                            </button>
                        </div>
                    </div>
                </div>
                {/* Bỏ WorkflowTypeNames checkboxes */}
            </div>
        </div>
    );
}
